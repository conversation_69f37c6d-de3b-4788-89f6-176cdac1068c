import json
import logging
import time
from collections import defaultdict
from typing import Optional

import numpy as np
import pandas as pd

from api.dto.MaterialDTO import MaterialDTO, MaterialSegmentDTO, MaterialSegmentSubtitleDTO
from core.constant import EMPTY_PAGING_RESPONSE
from core.func import get_user_identity
from repositories.es.model.MaterialDoc import MaterialDoc
from repositories.material_es_repository import material_es_repository
from repositories.material_vector_repository import material_vector_repository
from repositories.video_file_ata_repository import video_file_ata_repository
from sdk.clip_ai_yk import clip_ai_yk
from sdk.dify.subtitle_match_app import subtitle_match_app
from services.material_usage_amount_service import material_usage_amount_service
from tasks import voice_insight
from utils.common_util import remove_punctuation

logger = logging.getLogger(__name__)


class MaterialSearchEngine:

    @classmethod
    def search(cls, **kwargs):
        """
        搜索素材
        """
        pass


class MaterialSearchService:

    @classmethod
    def search(cls, search_engine: MaterialSearchEngine, **kwargs):
        return search_engine.search(**kwargs)


class MaterialEsSearchEngine(MaterialSearchEngine):
    """
    es 搜索素材
    """

    @classmethod
    def search(
            cls,
            ids: Optional[list[int]] = None,
            folderIds: Optional[list[int]] = None,
            vertical: Optional[int] = None,
            createTimeStart: Optional[str] = None,
            createTimeEnd: Optional[str] = None,
            pageNo: int = 1,
            pageSize: int = 20,
            **kwargs
    ):
        cid, oid = get_user_identity()

        response = material_es_repository.search_(
            cid, oid, ids, folderIds, vertical, createTimeStart,
            createTimeEnd, pageNo, pageSize
        )
        material_dto_list = []
        for hit in response.get('hits', {}).get('hits', []):

            hit_source = hit.get('_source', {})
            material_dto = MaterialDTO(**hit_source)
            material_dto.materialId = material_dto.id
            low_quality_url = hit_source.get('low_quality_url')

            if low_quality_url and len(low_quality_url) > 0:
                material_dto.url = low_quality_url

            if material_dto.folder_id == 0:
                material_dto.folder_name = '个人' if hit_source.get('user_id', 0) > 0 else '企业'

            material_dto_list.append(material_dto)

        return {
            'list': material_dto_list,
            'total': response.get('hits', {}).get('total', {}).get('value', 0)
        }


class MaterialVectorSearchEngine(MaterialSearchEngine):
    """
    milvus向量搜索素材
    """

    @classmethod
    def search(
            cls,
            duration: int,
            image: Optional[str] = None,
            keyword: Optional[str] = None,
            keywords: Optional[list[str]] = None,
            excludedIds: Optional[list[int]] = None,
            folderIds: Optional[list[int]] = None,
            vertical: Optional[int] = None,
            createTimeStart: Optional[str] = None,
            createTimeEnd: Optional[str] = None,
            pageNo: int = 1,
            pageSize: int = 20,
            **kwargs
    ):

        duration = int(duration / 1000) if duration > 100 else duration

        if (image is None or len(image) == 0) and (keyword is None or len(keyword) == 0):
            if keywords is None or len(keywords) == 0:
                return EMPTY_PAGING_RESPONSE
        logger.info(f'milvus query {keyword}')

        if keywords is not None and len(keywords) > 0:
            vector_data = [[clip_ai_yk.embed(text=keyword)] for keyword in keywords]
        else:
            vector_data = [[clip_ai_yk.embed(image, keyword)]]

        vector_data = list(filter(lambda x: x is not None, vector_data))
        logger.info(f'milvus query {len(vector_data)}')

        if len(vector_data) == 0:
            return EMPTY_PAGING_RESPONSE

        result = []
        for vector in vector_data:
            result.extend(cls._vector_query_single(
                vector, duration, excludedIds, folderIds, vertical,
                createTimeStart, createTimeEnd, pageNo, pageSize
            ))

        if len(result) == 0:
            return EMPTY_PAGING_RESPONSE

        df = pd.DataFrame(result)

        return {
            'list': df.loc[df.groupby('id')['score'].idxmax()].sort_values(by='score', ascending=False).head(
                pageSize).to_dict(
                orient='records'),
            'total': 999
        }

    @classmethod
    def _vector_query_single(
            cls,
            vector_data: list,
            duration: int,
            excluded_ids: Optional[list[int]] = None,
            folder_ids: Optional[list[int]] = None,
            vertical: Optional[int] = None,
            create_time_start: Optional[str] = None,
            create_time_end: Optional[str] = None,
            pageNo: int = 1,
            pageSize: int = 20,
            **kwargs
    ) -> list[dict]:
        cid, oid = get_user_identity()

        offset = (pageNo - 1) * pageSize

        # 通过向量找到的文件列表
        file_vector_list = material_vector_repository.vector_query_file_id(
            cid, oid, vector_data, duration, excluded_ids,
            folder_ids, vertical, create_time_start, create_time_end
        )[offset:offset + pageSize]
        if len(file_vector_list) == 0:
            return []

        # 文件id转换成当前用户的素材id
        file_ids = [file_vector['file_id'] for file_vector in file_vector_list]
        material_ids = cls._to_material_id(user_id=cid, organize_id=oid, file_ids=file_ids, folder_ids=folder_ids)

        # 通过素材id获取素材的所有分片向量 按照素材分组
        material_vector_list = material_vector_repository.get_by_material_ids(material_ids)

        material_vector_group: dict[int, list] = defaultdict(list)
        [material_vector_group[material_vector.material_id].append(material_vector.model_dump())
         for material_vector in material_vector_list]

        # 通过滑动窗口获取最合适的片段
        best_clip_list = cls._find_best_clip_in_group(material_vector_group, vector_data[0], duration)
        best_clip_map = {best_clip['material_id']: best_clip for best_clip in best_clip_list}
        material_ids = [best_clip['material_id'] for best_clip in best_clip_list]

        # 获取素材信息
        material_doc_list = material_es_repository.get_by_ids(material_ids)

        material_dto_list = []
        for material_doc in material_doc_list:
            material_dto = MaterialSegmentDTO(**material_doc.model_dump())

            best_clip = best_clip_map[material_dto.id]
            material_dto.clip_start = best_clip['clip_start']
            material_dto.clip_duration = best_clip['clip_end'] - best_clip['clip_start']
            material_dto.score = best_clip['score']
            material_dto.materialId = material_dto.id
            material_dto_list.append(material_dto.model_dump())

        return material_dto_list

    @classmethod
    def _normalize_vectors(cls, vectors):
        """
        归一化向量
        :param vectors:
        :return:
        """
        vectors = np.array(vectors)
        norms = np.linalg.norm(vectors, axis=1, keepdims=True)
        return vectors / (norms + 1e-8)

    @classmethod
    def _find_best_clip_in_group(cls, vector_group, image_vector, duration):
        """
        在每个素材的向量组中找到最佳片段
        :param vector_group:
        :param image_vector:
        :param duration:
        :return:
        """

        norm_image_vector = np.array(image_vector)
        norm_image_vector = norm_image_vector / (np.linalg.norm(norm_image_vector) + 1e-8)

        best_clips = []

        # 获取所有素材的使用量数据
        material_ids = list(vector_group.keys())
        usage_data = material_usage_amount_service.get_usage_by_day(material_ids, 7)  # 获取最近7天的合并数据

        for material_id in vector_group:
            group = sorted(vector_group[material_id], key=lambda x: x['clip_start'])

            # 提取和归一化所有帧向量
            vectors = [item['frame_vector'] for item in group]
            norm_vectors = cls._normalize_vectors(vectors)

            # 与 image_vector 做余弦相似度（点积）
            similarities = np.dot(norm_vectors, norm_image_vector)

            # 对相似度进行降权
            usage_array = usage_data.get(material_id, [])
            if usage_array:
                # 确保usage_array长度足够
                for i in range(len(similarities)):
                    # 将毫秒转换为秒（索引）
                    frame_index = i  # 这里i就是秒级的索引
                    if frame_index < len(usage_array):
                        usage_count = usage_array[frame_index]
                        # 每使用一次降低0.02分
                        similarities[i] -= usage_count * 0.02

            # 滑动窗口求平均相似度
            window_sums = np.convolve(similarities, np.ones(duration), mode='valid')
            avg_similarities = window_sums / duration

            # 找最佳起点
            best_start_idx = np.argmax(avg_similarities)
            best_score = avg_similarities[best_start_idx]

            best_clips.append({
                'clip_start': int(best_start_idx) * 1000,
                'clip_end': (int(best_start_idx) + duration) * 1000,
                'score': float(best_score),
                'material_id': material_id
            })

        return best_clips

    @classmethod
    def _to_material_id(cls, user_id, organize_id, file_ids, folder_ids):
        """
        转换文件id到当前用户的素材id

        :param user_id:
        :param organize_id:
        :param file_ids:
        :param folder_ids:
        :return:
        """
        should = [
            {'bool': {'must': [{'term': {'organize_id': organize_id}}, {'term': {'user_id': user_id}}]}},
            {'bool': {'must': [{'term': {'organize_id': organize_id}}, {'term': {'user_id': 0}}]}}
        ]

        must = [
            {"bool": {"should": should}},
            {"terms": {"file_id": file_ids}},
        ]
        if folder_ids is not None and len(folder_ids) > 0:
            must.append({"terms": {"folder_ids": folder_ids}})

        query = {
            "size": 0,
            "query": {
                "bool": {
                    "must": must
                }
            },
            "aggs": {
                "group_by_file_id": {
                    "terms": {
                        "field": "file_id",
                        "size": len(file_ids)
                    },
                    "aggs": {
                        "top_docs": {
                            "top_hits": {
                                "size": 1
                            }
                        }
                    }
                }
            }
        }

        logger.info(f'query: {json.dumps(query)}')
        response = material_es_repository.base_search(body=query)

        group_by_file_buckets = response.get('aggregations', {}).get('group_by_file_id', {}).get('buckets', [])

        return [bucket.get('top_docs', {}).get('hits', {}).get('hits', [])[0]['_source']['id']
                for bucket in group_by_file_buckets if len(bucket.get('top_docs', {}).get('hits', {}).get('hits', []))]


class MaterialLLmSearchEngine(MaterialSearchEngine):
    """
    基于llm的搜索素材
    """

    @classmethod
    def search(cls, keyword, materialIds):
        """
        基于llm的搜索素材
        :param keyword: str
        :param materialIds: [int]
        :return: dict with 'list' and 'total'
        """
        if not materialIds:
            return EMPTY_PAGING_RESPONSE

        material_list = material_es_repository.get_by_ids(materialIds)
        if not material_list:
            return EMPTY_PAGING_RESPONSE

        # 获取视频文件的字幕信息
        file_video_file_ata_map = cls._get_material_file_utterances(material_list)

        # 基于llm匹配素材字幕
        llm_match_result = cls._llm_match(keyword, material_list, file_video_file_ata_map)

        if not llm_match_result:
            return EMPTY_PAGING_RESPONSE

        # 匹配素材字幕轴确定开始结束时间
        material_dto_list = cls._build_material_segments(
            keyword, llm_match_result, material_list, file_video_file_ata_map
        )

        return {
            'list': material_dto_list,
            'total': len(material_dto_list)
        }

    @classmethod
    def _build_material_segments(cls, keyword, llm_match_result, material_list, file_video_file_ata_map):
        """
        根据LLM匹配结果构建素材片段信息
        
        :param llm_match_result: LLM匹配结果列表
        :param material_list: 素材列表
        :param file_video_file_ata_map: 文件字幕映射
        :return: MaterialSegmentSubtitleDTO列表
        """
        # 将素材列表转换为字典方便查找
        material_map = {str(material.id): material for material in material_list}

        material_dto_list = []

        for match_item in llm_match_result:
            try:
                material_id = match_item.get('id')
                matched_text = match_item.get('subtitle', '')

                if not material_id or material_id not in material_map:
                    continue

                material = material_map[material_id]
                video_file_ata = file_video_file_ata_map.get(material.file_id)

                if not video_file_ata:
                    continue

                # 使用新的统一方法匹配时间，获取详细的词信息
                clip_info = cls.match_clip_time_with_words(matched_text, video_file_ata)

                if not clip_info:
                    continue

                # 构建MaterialSegmentSubtitleDTO
                material_dto = MaterialSegmentSubtitleDTO(**material.model_dump())
                material_dto.clip_start = clip_info['start_time']
                material_dto.clip_duration = clip_info['end_time'] - clip_info['start_time']
                material_dto.score = cls._jaccard_similarity(keyword, matched_text)

                # 填充sub_ata和last_word
                if clip_info.get('words'):
                    material_dto.sub_ata = clip_info['words']
                    material_dto.last_word = clip_info['words'][-1] if clip_info['words'] else None

                material_dto_list.append(material_dto.model_dump())

            except Exception as e:
                logger.error(f"处理素材片段失败 material_id={match_item.get('id')}: {e}")
                continue

        # 按照分数排序
        material_dto_list.sort(key=lambda x: x.get('score', 0), reverse=True)

        return material_dto_list

    @classmethod
    def _jaccard_similarity(cls, str1, str2):
        set1, set2 = set(str1), set(str2)
        return len(set1 & set2) / len(set1 | set2)

    @classmethod
    def _find_clip_in_utterances(cls, utterances, matched_text):
        """
        在utterances中查找匹配文本的时间信息
        
        :param utterances: 字幕信息列表
        :param matched_text: 匹配的文本
        :return: dict with start_time and end_time
        """
        if not matched_text:
            return None

        # 清理匹配文本，去除空格和标点
        clean_matched_text = ''.join(matched_text.split())

        # 构建所有字的列表，包含时间信息
        all_words = []
        for utterance in utterances:
            words = utterance.get('words', [])
            for word in words:
                all_words.append({
                    'text': word.get('text', ''),
                    'start_time': word.get('start_time', 0),
                    'end_time': word.get('end_time', 0),
                    'utterance_start': utterance.get('start_time', 0),
                    'utterance_end': utterance.get('end_time', 0)
                })

        # 尝试精确匹配
        clip_info = cls._exact_match_words(all_words, matched_text, clean_matched_text)

        if clip_info:
            return clip_info

        # 如果精确匹配失败，尝试模糊匹配
        return cls._fuzzy_match_words(all_words, matched_text)

    @classmethod
    def _exact_match_words(cls, all_words, matched_text, clean_matched_text):
        """
        在words列表中精确匹配文本
        
        :param all_words: 所有字的列表
        :param matched_text: 原始匹配文本
        :param clean_matched_text: 清理后的匹配文本
        :return: dict with start_time and end_time
        """
        # 尝试在连续的字中找到匹配
        for i in range(len(all_words)):
            # 从当前位置开始，尝试匹配
            combined_text = ''
            clean_combined = ''

            for j in range(i, min(i + len(clean_matched_text) * 2, len(all_words))):
                combined_text += all_words[j]['text']
                clean_combined += all_words[j]['text']

                # 检查是否匹配
                if (matched_text in combined_text or
                        clean_matched_text == clean_combined or
                        clean_matched_text in clean_combined):

                    # 确定精确的开始和结束位置
                    start_idx, end_idx = cls._find_exact_boundary(
                        all_words[i:j + 1], matched_text, clean_matched_text
                    )

                    if start_idx is not None and end_idx is not None:
                        return {
                            'start_time': all_words[i + start_idx]['start_time'],
                            'end_time': all_words[i + end_idx]['end_time']
                        }

        return None

    @classmethod
    def _find_exact_boundary(cls, word_segment, matched_text, clean_matched_text):
        """
        在word段中找到精确的边界
        
        :param word_segment: 字的片段
        :param matched_text: 原始匹配文本
        :param clean_matched_text: 清理后的匹配文本
        :return: (start_idx, end_idx) or (None, None)
        """
        # 构建片段文本
        segment_text = ''.join(word['text'] for word in word_segment)

        # 如果整个片段就是匹配文本
        if segment_text == matched_text or segment_text == clean_matched_text:
            return 0, len(word_segment) - 1

        # 尝试在片段中找到匹配文本的位置
        if matched_text in segment_text:
            # 计算字符级别的偏移
            offset = segment_text.index(matched_text)
            char_count = 0
            start_idx = None
            end_idx = None

            for idx, word in enumerate(word_segment):
                word_len = len(word['text'])

                # 找到开始位置
                if start_idx is None and char_count + word_len > offset:
                    start_idx = idx

                # 找到结束位置
                if char_count < offset + len(matched_text) <= char_count + word_len:
                    end_idx = idx
                elif char_count + word_len >= offset + len(matched_text):
                    end_idx = idx
                    break

                char_count += word_len

            return start_idx, end_idx

        return None, None

    @classmethod
    def _fuzzy_match_words(cls, all_words, matched_text):
        """
        模糊匹配：找到包含最多匹配字符的连续片段
        
        :param all_words: 所有字的列表
        :param matched_text: 匹配的文本
        :return: dict with start_time and end_time
        """
        best_match = None
        best_score = 0

        # 将匹配文本分字
        match_chars = list(matched_text.replace(' ', ''))

        # 使用滑动窗口查找最佳匹配
        window_size = len(match_chars)

        for i in range(len(all_words) - window_size + 1):
            window_text = ''.join(all_words[j]['text'] for j in range(i, min(i + window_size, len(all_words))))

            # 计算匹配分数
            score = 0
            for char in match_chars:
                if char in window_text:
                    score += 1

            score = score / len(match_chars) if match_chars else 0

            if score > best_score and score > 0.6:  # 至少60%的字符匹配
                best_score = score
                # 找到包含最多匹配字符的最小范围
                start_idx = i
                end_idx = min(i + window_size - 1, len(all_words) - 1)

                # 优化边界：去除首尾不匹配的字
                while start_idx < end_idx and all_words[start_idx]['text'] not in matched_text:
                    start_idx += 1
                while end_idx > start_idx and all_words[end_idx]['text'] not in matched_text:
                    end_idx -= 1

                best_match = {
                    'start_time': all_words[start_idx]['start_time'],
                    'end_time': all_words[end_idx]['end_time']
                }

        return best_match

    @classmethod
    def _llm_match(cls, keyword, material_list, file_video_file_ata_map):
        """
        调用llm匹配字幕

        :return list[dict]  [{id:int,subtitle:str}]
        """
        subtitle_list = []
        for material in material_list:
            video_file_ata = file_video_file_ata_map.get(material.file_id, None)
            if video_file_ata is None or video_file_ata.utterances is None or len(video_file_ata.utterances) == 0:
                continue
            try:
                utterances = json.loads(video_file_ata.utterances)
                subtitle = [utterance.get('text', '') for utterance in utterances]
            except Exception as _:
                continue

            subtitle_list.append({
                'id': material.id,
                'subtitle': subtitle,
            })

        matched_list = subtitle_match_app.run({
            'subtitle': json.dumps(subtitle_list, ensure_ascii=False),
            'match': keyword
        }, 'material-ai-yk')

        result = []
        exist_subtitle = []
        for matched in matched_list:
            if matched.get('id') is None:
                continue
            matched['id'] = str(matched['id'])
            if len(matched['id']) == 0:
                continue

            if matched.get('subtitle') is None or len(matched['subtitle']) == 0:
                continue

            no_punctuation_subtitle = remove_punctuation(matched['subtitle'])
            if no_punctuation_subtitle in exist_subtitle:
                continue

            exist_subtitle.append(no_punctuation_subtitle)
            result.append(matched)

        return result

    @classmethod
    def _get_material_file_utterances(cls, material_list: list[MaterialDoc]):
        """
        获取视频文件的字幕信息
        :param material_list:
        :return: dict[int,VideoFileAta]
        """
        file_ids = [material.file_id for material in material_list]

        # 获取视频文件的字幕信息
        file_utterances_map = video_file_ata_repository.get_by_file_ids(file_ids)
        not_utterances_file_ids = [file_id for file_id in file_ids if file_utterances_map.get(file_id) is None]

        # 补充不存在字幕的视频文件
        logger.info(f'开始补充不存在字幕的视频文件: {not_utterances_file_ids}')
        task_list = [voice_insight.delay(material.file_id, material.id) for material in material_list if
                     material.file_id in not_utterances_file_ids]
        for task in task_list:
            task.get(timeout=10)

        # 重新获取视频文件的字幕信息
        return video_file_ata_repository.get_by_file_ids(
            [material.file_id for material in material_list])

    @classmethod
    def match_clip_time(cls, matched_text, video_file_ata):
        """
        匹配单个素材的开始结束时间
        
        :param matched_text: 需要匹配的文本
        :param video_file_ata: VideoFileAta对象
        :return: dict with start_time and end_time, or None if not found
        """
        if not matched_text or not video_file_ata or not video_file_ata.utterances:
            return None

        try:
            # 解析utterances
            utterances = json.loads(video_file_ata.utterances)
            if not utterances:
                return None

            # 在utterances中查找匹配的文本段落
            return cls._find_clip_in_utterances(utterances, matched_text)

        except Exception as e:
            logger.error(f"匹配素材时间失败: {e}")
            return None

    @classmethod
    def match_clip_time_with_words(cls, matched_text, video_file_ata):
        """
        匹配单个素材的开始结束时间，并返回详细的词信息
        
        :param matched_text: 需要匹配的文本
        :param video_file_ata: VideoFileAta对象
        :return: dict with start_time, end_time and words list, or None if not found
        """
        if not matched_text or not video_file_ata or not video_file_ata.utterances:
            return None

        try:
            # 解析utterances
            utterances = json.loads(video_file_ata.utterances)
            if not utterances:
                return None

            # 在utterances中查找匹配的文本段落，获取详细词信息
            return cls._find_clip_in_utterances_with_words(utterances, matched_text)

        except Exception as e:
            logger.error(f"匹配素材时间失败: {e}")
            return None

    @classmethod
    def _find_clip_in_utterances_with_words(cls, utterances, matched_text):
        """
        在utterances中查找匹配文本的时间信息和详细词信息
        
        :param utterances: 字幕信息列表
        :param matched_text: 匹配的文本
        :return: dict with start_time, end_time and words list
        """
        if not matched_text:
            return None

        # 清理匹配文本，去除空格和标点
        clean_matched_text = ''.join(matched_text.split())

        # 构建所有字的列表，包含时间信息
        all_words = []
        for utterance in utterances:
            words = utterance.get('words', [])
            for word in words:
                all_words.append({
                    'text': word.get('text', ''),
                    'start_time': word.get('start_time', 0),
                    'end_time': word.get('end_time', 0),
                    'attribute': word.get('attribute', utterance.get('attribute', {})),
                    'utterance_start': utterance.get('start_time', 0),
                    'utterance_end': utterance.get('end_time', 0)
                })

        # 尝试精确匹配，获取词范围
        clip_info = cls._exact_match_words_with_range(all_words, matched_text, clean_matched_text)

        if clip_info:
            return clip_info

        # 如果精确匹配失败，尝试模糊匹配
        return cls._fuzzy_match_words_with_range(all_words, matched_text)

    @classmethod
    def _exact_match_words_with_range(cls, all_words, matched_text, clean_matched_text):
        """
        在words列表中精确匹配文本，返回包含词列表的详细信息
        
        :param all_words: 所有字的列表
        :param matched_text: 原始匹配文本
        :param clean_matched_text: 清理后的匹配文本
        :return: dict with start_time, end_time and words list
        """
        # 尝试在连续的字中找到匹配
        for i in range(len(all_words)):
            # 从当前位置开始，尝试匹配
            combined_text = ''
            clean_combined = ''

            for j in range(i, min(i + len(clean_matched_text) * 2, len(all_words))):
                combined_text += all_words[j]['text']
                clean_combined += all_words[j]['text']

                # 检查是否匹配
                if (matched_text in combined_text or
                        clean_matched_text == clean_combined or
                        clean_matched_text in clean_combined):

                    # 确定精确的开始和结束位置
                    start_idx, end_idx = cls._find_exact_boundary(
                        all_words[i:j + 1], matched_text, clean_matched_text
                    )

                    if start_idx is not None and end_idx is not None:
                        actual_start_idx = i + start_idx
                        actual_end_idx = i + end_idx

                        # 提取匹配的词列表
                        matched_words = []
                        for word_idx in range(actual_start_idx, actual_end_idx + 1):
                            word = all_words[word_idx]
                            matched_words.append({
                                'text': word['text'],
                                'start_time': word['start_time'],
                                'end_time': word['end_time'],
                                'attribute': word.get('attribute', {})
                            })

                        return {
                            'start_time': all_words[actual_start_idx]['start_time'],
                            'end_time': all_words[actual_end_idx]['end_time'],
                            'words': matched_words
                        }

        return None

    @classmethod
    def _fuzzy_match_words_with_range(cls, all_words, matched_text):
        """
        模糊匹配：找到包含最多匹配字符的连续片段，返回包含词列表的详细信息
        
        :param all_words: 所有字的列表
        :param matched_text: 匹配的文本
        :return: dict with start_time, end_time and words list
        """
        best_match = None
        best_score = 0
        best_range = None

        # 将匹配文本分字
        match_chars = list(matched_text.replace(' ', ''))

        # 使用滑动窗口查找最佳匹配
        window_size = len(match_chars)

        for i in range(len(all_words) - window_size + 1):
            window_text = ''.join(all_words[j]['text'] for j in range(i, min(i + window_size, len(all_words))))

            # 计算匹配分数
            score = 0
            for char in match_chars:
                if char in window_text:
                    score += 1

            score = score / len(match_chars) if match_chars else 0

            if score > best_score and score > 0.6:  # 至少60%的字符匹配
                best_score = score
                # 找到包含最多匹配字符的最小范围
                start_idx = i
                end_idx = min(i + window_size - 1, len(all_words) - 1)

                # 优化边界：去除首尾不匹配的字
                while start_idx < end_idx and all_words[start_idx]['text'] not in matched_text:
                    start_idx += 1
                while end_idx > start_idx and all_words[end_idx]['text'] not in matched_text:
                    end_idx -= 1

                best_range = (start_idx, end_idx)
                best_match = {
                    'start_time': all_words[start_idx]['start_time'],
                    'end_time': all_words[end_idx]['end_time']
                }

        # 如果找到了最佳匹配，添加词列表信息
        if best_match and best_range:
            start_idx, end_idx = best_range
            matched_words = []
            for word_idx in range(start_idx, end_idx + 1):
                word = all_words[word_idx]
                matched_words.append({
                    'text': word['text'],
                    'start_time': word['start_time'],
                    'end_time': word['end_time'],
                    'attribute': word.get('attribute', {})
                })

            best_match['words'] = matched_words

        return best_match


material_search_service = MaterialSearchService()
